-- Fix UUID schema: Convert TEXT back to UUID for id columns
-- This script reverts the changes made in migration 20250623112936_create_default_uuid_with_string

-- First, drop the unique constraints that were added
DROP INDEX IF EXISTS "conversations_id_key";
DROP INDEX IF EXISTS "messages_id_key";

-- Convert conversations.id back to UUID
ALTER TABLE "conversations" DROP CONSTRAINT "conversations_pkey" CASCADE;
ALTER COLUMN "id" SET DATA TYPE UUID USING id::uuid;
ADD CONSTRAINT "conversations_pkey" PRIMARY KEY ("id");

-- Convert messages.id back to UUID  
ALTER TABLE "messages" DROP CONSTRAINT "messages_pkey";
ALTER COLUMN "id" SET DATA TYPE UUID USING id::uuid;
ADD CONSTRAINT "messages_pkey" PRIMARY KEY ("id");

-- Update foreign key references to use UUID
ALTER TABLE "messages" 
ALTER COLUMN "conversation_id" SET DATA TYPE UUID USING conversation_id::uuid;

-- Recreate foreign key constraints
ALTER TABLE "messages" 
ADD CONSTRAINT "messages_conversation_id_fkey" 
FOREIGN KEY ("conversation_id") REFERENCES "conversations"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- Verify the changes
SELECT 
    table_name, 
    column_name, 
    data_type 
FROM information_schema.columns 
WHERE table_name IN ('conversations', 'messages') 
    AND column_name IN ('id', 'conversation_id')
ORDER BY table_name, column_name;
