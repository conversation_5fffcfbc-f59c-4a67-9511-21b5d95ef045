# GenTekai Backend

GenTekai is a multi-agent platform that uses AI to help with HR, recruitment, onboarding, and employee management tasks.

## Development

```bash
# Start development environment (all services)
docker compose up -d

# View logs
docker compose logs -f

# Stop services
docker compose down
```

## Production Deployment

### Prerequisites

1. **Server Setup**: Docker with Compose V2 installed
2. **GitHub Secrets**: Configure in repository settings
   - `SSH_HOST`: Server hostname/IP
   - `SSH_USER`: SSH username
   - `SSH_PRIVATE_KEY`: SSH private key

### Automatic Deployment

Push to `main` or `master` branch triggers automatic deployment:

1. **Build**: Docker image built with commit SHA
2. **Deploy**: Image transferred to server and deployed
3. **Health Check**: Verify application is running

### Manual Server Operations

```bash
# Deploy
./scripts/deploy.sh deploy

# Check status
./scripts/deploy.sh status

# View logs
./scripts/deploy.sh logs

# Restart
./scripts/deploy.sh restart

# Rollback
./scripts/deploy.sh rollback

# Health check
./scripts/deploy.sh health
```

### Production vs Development

- **Development**: `docker-compose.yml` (backend + database + MCP server)
- **Production**: `docker-compose.prod.yml` (backend only)

### Health Check

Application health endpoint: `/api/v1/health`

curl http://localhost:9876/api/v1/health
```

## Architecture

- **Backend**: FastAPI application (port 9876)
- **Database**: PostgreSQL with pgvector
- **MCP Server**: SQL tool server (development only)

## Environment Variables

Create `.env.secrets` file:

```env
DATABASE_URL=postgresql://user:pass@host:port/db
SQL_TOOL_URL=http://mcp-server:3002/sse
# Add other secrets...
```

## Development Commands

```bash
# Install dependencies
uv sync

# Run development server
uv run uvicorn gentekai.main:app --reload

# Lint and format
uv run ruff check --fix .
uv run ruff format .
```
