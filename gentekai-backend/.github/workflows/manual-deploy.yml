name: Manual Deployment

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'production'
        type: choice
        options:
          - production
          - staging
      action:
        description: 'Action to perform'
        required: true
        default: 'deploy'
        type: choice
        options:
          - deploy
          - rollback
          - restart
          - status
          - logs
      branch:
        description: 'Branch to deploy (for deploy action)'
        required: false
        default: 'main'
        type: string
      log_lines:
        description: 'Number of log lines to show (for logs action)'
        required: false
        default: '100'
        type: string

env:
  DOCKER_COMPOSE_FILE: docker-compose.yml
  PROJECT_NAME: gentekai-backend
  HEALTH_CHECK_URL: http://localhost:9876/api/v1/health

jobs:
  manual-operation:
    name: ${{ inputs.action }} - ${{ inputs.environment }}
    runs-on: ubuntu-latest
    
    environment:
      name: ${{ inputs.environment }}
    
    steps:
      - name: Checkout code
        if: inputs.action == 'deploy'
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.branch }}

      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.8.0
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: Add server to known hosts
        run: |
          ssh-keyscan -H ${{ secrets.SSH_HOST }} >> ~/.ssh/known_hosts

      - name: Deploy Application
        if: inputs.action == 'deploy'
        run: |
          # Build and deploy
          docker build -t ${{ env.PROJECT_NAME }}:${{ github.sha }} -f Dockerfile.dev .
          docker save ${{ env.PROJECT_NAME }}:${{ github.sha }} > /tmp/image.tar
          
          scp /tmp/image.tar ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }}:~/deployments/${{ env.PROJECT_NAME }}/
          scp ${{ env.DOCKER_COMPOSE_FILE }} ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }}:~/deployments/${{ env.PROJECT_NAME }}/
          
          ssh ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "
            cd ~/deployments/${{ env.PROJECT_NAME }}
            docker load -i image.tar
            docker tag ${{ env.PROJECT_NAME }}:${{ github.sha }} ${{ env.PROJECT_NAME }}:latest
            docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} down --timeout 30
            docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} up -d
            rm -f image.tar
          "

      - name: Rollback Application
        if: inputs.action == 'rollback'
        run: |
          ssh ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "
            cd ~/deployments/${{ env.PROJECT_NAME }}
            
            # Get previous image
            PREVIOUS_IMAGE=\$(docker images ${{ env.PROJECT_NAME }} --format '{{.ID}} {{.CreatedAt}}' | sort -k2 -r | sed -n '2p' | awk '{print \$1}')
            
            if [ -z \"\$PREVIOUS_IMAGE\" ]; then
              echo 'No previous image found!'
              exit 1
            fi
            
            docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} down --timeout 30
            docker tag \$PREVIOUS_IMAGE ${{ env.PROJECT_NAME }}:latest
            docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} up -d
          "

      - name: Restart Application
        if: inputs.action == 'restart'
        run: |
          ssh ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "
            cd ~/deployments/${{ env.PROJECT_NAME }}
            docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} restart
          "

      - name: Check Status
        if: inputs.action == 'status'
        run: |
          ssh ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "
            cd ~/deployments/${{ env.PROJECT_NAME }}
            echo '=== Container Status ==='
            docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} ps
            echo ''
            echo '=== Resource Usage ==='
            docker stats --no-stream --format 'table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}'
            echo ''
            echo '=== Disk Usage ==='
            df -h
            echo ''
            echo '=== Docker Images ==='
            docker images ${{ env.PROJECT_NAME }}
          "

      - name: Show Logs
        if: inputs.action == 'logs'
        run: |
          ssh ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "
            cd ~/deployments/${{ env.PROJECT_NAME }}
            echo '=== Application Logs (last ${{ inputs.log_lines }} lines) ==='
            docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} logs --tail=${{ inputs.log_lines }}
          "

      - name: Health Check
        if: inputs.action == 'deploy' || inputs.action == 'rollback' || inputs.action == 'restart'
        run: |
          ssh ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "
            for i in {1..10}; do
              if curl -f ${{ env.HEALTH_CHECK_URL }} >/dev/null 2>&1; then
                echo '✅ Health check passed!'
                break
              fi
              echo 'Waiting for application... (\$i/10)'
              sleep 10
            done
            
            if ! curl -f ${{ env.HEALTH_CHECK_URL }} >/dev/null 2>&1; then
              echo '❌ Health check failed!'
              exit 1
            fi
          "

      - name: Summary
        if: always()
        run: |
          echo "## Deployment Summary" >> $GITHUB_STEP_SUMMARY
          echo "- **Action**: ${{ inputs.action }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Environment**: ${{ inputs.environment }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Status**: ${{ job.status }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Timestamp**: $(date -u)" >> $GITHUB_STEP_SUMMARY
          
          if [ "${{ inputs.action }}" == "deploy" ]; then
            echo "- **Branch**: ${{ inputs.branch }}" >> $GITHUB_STEP_SUMMARY
            echo "- **Commit**: ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
          fi
