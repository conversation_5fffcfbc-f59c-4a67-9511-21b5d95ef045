name: Deploy to Production

on:
  push:
    branches: [ main, master ]

env:
  DOCKER_COMPOSE_FILE: docker-compose.prod.yml
  PROJECT_NAME: gentekai-backend
  HEALTH_CHECK_URL: http://localhost:9876/api/v1/health

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-24.04
    container: python:3.13.3
    timeout-minutes: 10
    services:
      db:
        image: postgres:17.5
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Install uv
        uses: astral-sh/setup-uv@v6
        with:
          version: ">=0.6,<0.7"
          enable-cache: true
          cache-dependency-glob: "**/uv.lock"
      - name: Install dependencies
        if: steps.setup-uv.outputs.cache-hit == 'true'
        run: uv sync
      - name: Run linters
        run: uv run ruff check .
      - name: Run tests
        run: uv run pytest
        env:
          CI: true

  deploy:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: test

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Build Docker image
        run: |
          docker build -t ${{ env.PROJECT_NAME }}:${{ github.sha }} -f Dockerfile.dev .
          docker save ${{ env.PROJECT_NAME }}:${{ github.sha }} > /tmp/image.tar

      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.8.0
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: Add server to known hosts
        run: |
          ssh-keyscan -H ${{ secrets.SSH_HOST }} >> ~/.ssh/known_hosts

      - name: Deploy to server
        run: |
          # Transfer files
          scp /tmp/image.tar ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }}:~/deployments/${{ env.PROJECT_NAME }}/
          scp ${{ env.DOCKER_COMPOSE_FILE }} ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }}:~/deployments/${{ env.PROJECT_NAME }}/

          # Deploy on server
          ssh ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "
            cd ~/deployments/${{ env.PROJECT_NAME }}

            # Load new image
            docker load -i image.tar
            docker tag ${{ env.PROJECT_NAME }}:${{ github.sha }} ${{ env.PROJECT_NAME }}:latest

            # Deploy
            docker compose -f ${{ env.DOCKER_COMPOSE_FILE }} down --timeout 30
            docker compose -f ${{ env.DOCKER_COMPOSE_FILE }} up -d

            # Cleanup
            rm -f image.tar
          "

      - name: Health check
        run: |
          ssh ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "
            for i in {1..10}; do
              if curl -f ${{ env.HEALTH_CHECK_URL }} >/dev/null 2>&1; then
                echo '✅ Deployment successful!'
                exit 0
              fi
              echo 'Waiting for application... (\$i/10)'
              sleep 10
            done
            echo '❌ Health check failed!'
            exit 1

