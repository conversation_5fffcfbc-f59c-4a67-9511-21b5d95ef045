name: Deploy to Production

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]
    types: [ closed ]

env:
  DOCKER_COMPOSE_FILE: docker-compose.yml
  PROJECT_NAME: gentekai-backend
  HEALTH_CHECK_URL: http://localhost:9876/api/v1/health

jobs:
  # Test job - runs on every push and PR
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    if: github.event_name == 'push' || (github.event_name == 'pull_request' && github.event.pull_request.merged == false)
    
    services:
      postgres:
        image: pgvector/pgvector:pg17
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.12'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt

      - name: Run linting
        run: |
          pip install flake8 black isort
          flake8 gentekai --count --select=E9,F63,F7,F82 --show-source --statistics
          black --check gentekai
          isort --check-only gentekai

      - name: Run tests
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
          TESTING: true
        run: |
          python -m pytest tests/ -v --cov=gentekai --cov-report=xml

      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage.xml
          flags: unittests
          name: codecov-umbrella

  # Build job - runs on main/master push and merged PRs
  build:
    name: Build Docker Images
    runs-on: ubuntu-latest
    needs: test
    if: (github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master')) || 
        (github.event_name == 'pull_request' && github.event.pull_request.merged == true)
    
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.PROJECT_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build Docker image
        id: build
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile.dev
          push: false
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          outputs: type=docker,dest=/tmp/image.tar

      - name: Upload Docker image artifact
        uses: actions/upload-artifact@v3
        with:
          name: docker-image
          path: /tmp/image.tar
          retention-days: 1

  # Deploy job - only runs on main/master push and merged PRs
  deploy:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [test, build]
    if: (github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master')) || 
        (github.event_name == 'pull_request' && github.event.pull_request.merged == true)
    
    environment:
      name: production
      url: ${{ env.HEALTH_CHECK_URL }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download Docker image artifact
        uses: actions/download-artifact@v3
        with:
          name: docker-image
          path: /tmp

      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.8.0
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: Add server to known hosts
        run: |
          ssh-keyscan -H ${{ secrets.SSH_HOST }} >> ~/.ssh/known_hosts

      - name: Create deployment directory
        run: |
          ssh ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "
            mkdir -p ~/deployments/${{ env.PROJECT_NAME }}/backup
            mkdir -p ~/deployments/${{ env.PROJECT_NAME }}/logs
          "

      - name: Copy Docker image to server
        run: |
          scp /tmp/image.tar ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }}:~/deployments/${{ env.PROJECT_NAME }}/

      - name: Copy deployment files to server
        run: |
          scp ${{ env.DOCKER_COMPOSE_FILE }} ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }}:~/deployments/${{ env.PROJECT_NAME }}/
          scp -r scripts/ ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }}:~/deployments/${{ env.PROJECT_NAME }}/ || true

      - name: Deploy application
        run: |
          ssh ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "
            cd ~/deployments/${{ env.PROJECT_NAME }}
            
            # Load the new Docker image
            docker load -i image.tar
            
            # Create backup of current deployment
            if docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} ps -q | grep -q .; then
              echo 'Creating backup of current deployment...'
              docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} ps > backup/containers-\$(date +%Y%m%d-%H%M%S).txt
              docker images --format 'table {{.Repository}}\t{{.Tag}}\t{{.ID}}\t{{.CreatedAt}}' > backup/images-\$(date +%Y%m%d-%H%M%S).txt
            fi
            
            # Stop current containers gracefully
            echo 'Stopping current containers...'
            docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} down --timeout 30
            
            # Clean up old images (keep last 3)
            echo 'Cleaning up old images...'
            docker images ${{ env.PROJECT_NAME }} --format '{{.ID}} {{.CreatedAt}}' | sort -k2 -r | tail -n +4 | awk '{print \$1}' | xargs -r docker rmi || true
            
            # Start new deployment
            echo 'Starting new deployment...'
            docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} up -d
            
            # Wait for services to be ready
            echo 'Waiting for services to start...'
            sleep 30
            
            # Verify deployment
            if docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} ps | grep -q 'Up'; then
              echo 'Deployment successful!'
              docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} ps
            else
              echo 'Deployment failed!'
              docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} logs --tail=50
              exit 1
            fi
          "

      - name: Health check
        run: |
          ssh ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "
            # Wait for application to be fully ready
            for i in {1..10}; do
              if curl -f ${{ env.HEALTH_CHECK_URL }} >/dev/null 2>&1; then
                echo 'Health check passed!'
                break
              fi
              echo 'Waiting for application to be ready... (\$i/10)'
              sleep 10
            done
            
            # Final health check
            if ! curl -f ${{ env.HEALTH_CHECK_URL }} >/dev/null 2>&1; then
              echo 'Health check failed after deployment!'
              docker-compose -f ~/deployments/${{ env.PROJECT_NAME }}/${{ env.DOCKER_COMPOSE_FILE }} logs --tail=100
              exit 1
            fi
          "

      - name: Cleanup artifacts
        run: |
          ssh ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "
            cd ~/deployments/${{ env.PROJECT_NAME }}
            rm -f image.tar
            
            # Keep only last 5 backups
            cd backup
            ls -t *.txt 2>/dev/null | tail -n +11 | xargs -r rm || true
          "

      - name: Notify deployment status
        if: always()
        run: |
          if [ '${{ job.status }}' == 'success' ]; then
            echo '✅ Deployment completed successfully!'
            echo 'Application is running at: ${{ env.HEALTH_CHECK_URL }}'
          else
            echo '❌ Deployment failed!'
            echo 'Please check the logs and server status.'
          fi

  # Rollback job - manual trigger only
  rollback:
    name: Rollback Deployment
    runs-on: ubuntu-latest
    if: github.event_name == 'workflow_dispatch'
    
    environment:
      name: production
    
    steps:
      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.8.0
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: Add server to known hosts
        run: |
          ssh-keyscan -H ${{ secrets.SSH_HOST }} >> ~/.ssh/known_hosts

      - name: Rollback to previous deployment
        run: |
          ssh ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "
            cd ~/deployments/${{ env.PROJECT_NAME }}
            
            echo 'Rolling back to previous deployment...'
            
            # Stop current containers
            docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} down --timeout 30
            
            # Get previous image
            PREVIOUS_IMAGE=\$(docker images ${{ env.PROJECT_NAME }} --format '{{.ID}} {{.CreatedAt}}' | sort -k2 -r | sed -n '2p' | awk '{print \$1}')
            
            if [ -z \"\$PREVIOUS_IMAGE\" ]; then
              echo 'No previous image found for rollback!'
              exit 1
            fi
            
            echo \"Rolling back to image: \$PREVIOUS_IMAGE\"
            
            # Tag previous image as latest
            docker tag \$PREVIOUS_IMAGE ${{ env.PROJECT_NAME }}:latest
            
            # Start with previous image
            docker-compose -f ${{ env.DOCKER_COMPOSE_FILE }} up -d
            
            # Verify rollback
            sleep 30
            if curl -f ${{ env.HEALTH_CHECK_URL }} >/dev/null 2>&1; then
              echo 'Rollback completed successfully!'
            else
              echo 'Rollback failed!'
              exit 1
            fi
          "
