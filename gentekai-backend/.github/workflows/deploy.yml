name: Deploy to Production

on:
  push:
    branches: [ main, master ]

env:
  DOCKER_COMPOSE_FILE: docker-compose.prod.yml
  PROJECT_NAME: gentekai-backend
  HEALTH_CHECK_URL: http://localhost:9876/api/v1/health

jobs:
  deploy:
    name: Deploy to Production
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Build Docker image
        run: |
          docker build -t ${{ env.PROJECT_NAME }}:${{ github.sha }} -f Dockerfile.dev .
          docker save ${{ env.PROJECT_NAME }}:${{ github.sha }} > /tmp/image.tar

      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.8.0
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: Add server to known hosts
        run: |
          ssh-keyscan -H ${{ secrets.SSH_HOST }} >> ~/.ssh/known_hosts

      - name: Deploy to server
        run: |
          # Transfer files
          scp /tmp/image.tar ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }}:~/deployments/${{ env.PROJECT_NAME }}/
          scp ${{ env.DOCKER_COMPOSE_FILE }} ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }}:~/deployments/${{ env.PROJECT_NAME }}/

          # Deploy on server
          ssh ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "
            cd ~/deployments/${{ env.PROJECT_NAME }}

            # Load new image
            docker load -i image.tar
            docker tag ${{ env.PROJECT_NAME }}:${{ github.sha }} ${{ env.PROJECT_NAME }}:latest

            # Deploy
            docker compose -f ${{ env.DOCKER_COMPOSE_FILE }} down --timeout 30
            docker compose -f ${{ env.DOCKER_COMPOSE_FILE }} up -d

            # Cleanup
            rm -f image.tar
          "

      - name: Health check
        run: |
          ssh ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "
            for i in {1..10}; do
              if curl -f ${{ env.HEALTH_CHECK_URL }} >/dev/null 2>&1; then
                echo '✅ Deployment successful!'
                exit 0
              fi
              echo 'Waiting for application... (\$i/10)'
              sleep 10
            done
            echo '❌ Health check failed!'
            exit 1

