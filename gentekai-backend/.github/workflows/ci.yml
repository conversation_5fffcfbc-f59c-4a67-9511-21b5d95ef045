name: CI

on:
	push:
		branches: [master]
		tags: [v*]
	pull_request:
		branches: [master]

jobs:
	test:
		runs-on: ubuntu-24.04
		container: python:3.13.3
		timeout-minutes: 10
		services:
			db:
				image: postgres:17.5
				env:
					POSTGRES_USER: postgres
					POSTGRES_PASSWORD: postgres
					POSTGRES_DB: test_db
				options: >-
					--health-cmd pg_isready
					--health-interval 10s
					--health-timeout 5s
					--health-retries 5
		steps:
			- name: Checkout
			  uses: actions/checkout@v4
			- name: Install uv
			  uses: astral-sh/setup-uv@v6
			  with:
				  version: ">=0.6,<0.7"
				  enable-cache: true
          cache-dependency-glob: "**/uv.lock"
			- name: Install dependencies
			  if: steps.setup-uv.outputs.cache-hit == 'true'
			  run: uv sync
			- name: Run linters
			  run: uv run ruff check .
			- name: Run tests
			  run: uv run pytest
			  env:
				  CI: true
