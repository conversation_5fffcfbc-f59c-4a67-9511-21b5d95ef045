import enum
from datetime import datetime

from sqlalchemy import Column, DateTime, Enum, <PERSON><PERSON>ey, Integer, String, Text
from sqlalchemy.orm import relationship

from gentekai.db.models.base import Base


class ApplicationStatus(enum.Enum):
    APPLIED = "applied"
    REVIEWING = "reviewing"
    INTERVIEW_SCHEDULED = (
        "interview_scheduled"  # Simple status instead of complex interview model
    )
    INTERVIEW_COMPLETED = "interview_completed"
    OFFER_EXTENDED = "offer_extended"
    HIRED = "hired"
    REJECTED = "rejected"
    WITHDRAWN = "withdrawn"


class Application(Base):
    __tablename__ = "applications"

    id = Column(Integer, primary_key=True)
    candidate_id = Column(
        String, ForeignKey("users.id"), nullable=False
    )  # References User
    position_id = Column(Integer, ForeignKey("positions.id"), nullable=False)

    # Application details
    applied_at = Column(DateTime, default=datetime.utcnow)
    status = Column(Enum(ApplicationStatus), default=ApplicationStatus.APPLIED)
    notes = Column(Text)

    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_updated_by_id = Column(String, ForeignKey("users.id"))

    # Relationships
    candidate = relationship(
        "User", foreign_keys=[candidate_id], back_populates="applications"
    )
    position = relationship("Position", back_populates="applications")
    last_updated_by = relationship("User", foreign_keys=[last_updated_by_id])
