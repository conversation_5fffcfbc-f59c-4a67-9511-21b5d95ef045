from sqlalchemy import UUID, Column, Foreign<PERSON>ey, Integer

from gentekai.db.models.base import Base


class UserSkill(Base):
    __tablename__ = "user_skills"
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    skill_id = Column(Integer, ForeignKey("skills.id"), nullable=False)
    proficiency = Column(Integer)  # e.g. 1-10 scale
    # Add unique constraint on (user_id, skill_id) if you wish
