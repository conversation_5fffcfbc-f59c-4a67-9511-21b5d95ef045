from datetime import datetime

from sqlalchemy import <PERSON>UID, <PERSON>umn, DateTime, Foreign<PERSON>ey, Integer, String
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship

from gentekai.db.models.base import Base


class WorkflowRecord(Base):
    __tablename__ = "workflow_records"
    id = Column(Integer, primary_key=True, autoincrement=True)
    type = Column(String(50))
    status = Column(String(20))
    user_id = Column(UUID(as_uuid=False), ForeignKey("users.id"), nullable=False)
    input_data = Column(JSONB)
    result_data = Column(JSONB)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    user = relationship("User", back_populates="workflows")
