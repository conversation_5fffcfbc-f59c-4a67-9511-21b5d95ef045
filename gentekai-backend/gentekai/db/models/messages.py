import uuid
from datetime import datetime

from pgvector.sqlalchemy import Vector
from sqlalchemy import (
    Boolean,
    Column,
    DateTime,
    ForeignKey,
    Index,
    Integer,
    String,
    Text,
)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship

from gentekai.db.models.base import Base


class Message(Base):
    __tablename__ = "messages"

    # Primary fields - using String to match database TEXT type but with UUID defaults
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    conversation_id = Column(
        String,
        ForeignKey("conversations.id", ondelete="CASCADE"),
        nullable=False,
    )
    sender = Column(String(20), nullable=False)  # 'user' or 'assistant'
    content = Column(Text, nullable=False)

    # Vector embeddings for semantic search
    embedding = Column(
        Vector(1536), nullable=True
    )  # pgvector extension; 1536-dim OpenAI embeddings

    # 🆕 field for enhanced supervisor analytics
    extra = Column(JSONB, nullable=True)  # Rich metadata as JSON

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )

    # AI Agent metadata
    agent_used = Column(
        String(50), nullable=True
    )  # Which agent generated this response
    response_time_ms = Column(Integer, nullable=True)  # Response time in milliseconds
    tokens_used = Column(Integer, nullable=True)  # Number of tokens used

    # Message metadata
    is_edited = Column(Boolean, default=False, nullable=False)
    edit_count = Column(Integer, default=0, nullable=False)
    character_count = Column(Integer, nullable=True)  # Denormalized for analytics

    # Soft delete (if needed at message level)
    is_deleted = Column(Boolean, default=False, nullable=False)
    deleted_at = Column(DateTime, nullable=True)

    # Relationships
    conversation = relationship("Conversation", back_populates="messages")

    # Indexes for performance
    __table_args__ = (
        # Primary query patterns
        Index("idx_messages_conversation_created", "conversation_id", "created_at"),
        Index("idx_messages_conversation_sender", "conversation_id", "sender"),
        # For analytics queries
        Index("idx_messages_sender_created", "sender", "created_at"),
        Index("idx_messages_created_at", "created_at"),
        # For vector search (if using pgvector)
        # Index('idx_messages_embedding_cosine', 'embedding', postgresql_using='ivfflat', postgresql_ops={'embedding': 'vector_cosine_ops'}),
        # For content search
        Index(
            "idx_messages_content_search", "content"
        ),  # Consider gin for full-text search
        # Soft delete support
        Index("idx_messages_deleted", "is_deleted"),
        # Agent performance analysis
        Index("idx_messages_agent_response_time", "agent_used", "response_time_ms"),
    )

    def __repr__(self):
        return f"<Message(id={self.id}, sender='{self.sender}', conversation_id={self.conversation_id})>"

    @property
    def content_preview(self) -> str:
        """Get a preview of the message content"""
        if not self.content:
            return ""
        return self.content[:100] + "..." if len(self.content) > 100 else self.content
