from datetime import datetime

from pgvector.sqlalchemy import Vector
from sqlalchemy import <PERSON><PERSON><PERSON>, Boolean, Column, DateTime, Foreign<PERSON>ey, Integer, String
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship

from gentekai.db.models.base import Base


class CVData(Base):
    __tablename__ = "cv_data"
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(UUID(as_uuid=False), ForeignKey("users.id"), nullable=False)
    name = Column(String(100), nullable=False)  # e.g., "Default", "For Google", etc.
    data = Column(JSONB, nullable=False)  # CV content, structure flexible

    # Additional fields for candidate CVs
    is_public = Column(Boolean, default=False)  # Whether HR can search this CV
    source = Column(String(50))  # How this CV was obtained (uploaded, parsed, etc.)

    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Vector embeddings for semantic search
    embedding = Column(
        Vector(1536), nullable=True
    )  # pgvector extension; 1536-dim OpenAI embeddings

    user = relationship("User", back_populates="cv_list")
