import enum
import uuid
from datetime import datetime

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, Enum, String
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship

from gentekai.db.models.base import Base


class UserRole(enum.Enum):
    OWNER = "owner"
    ADMIN = "admin"
    USER = "user"
    EXTERNAL = "external"


class User(Base):
    __tablename__ = "users"
    id = Column(
        String, primary_key=True, default=lambda: str(uuid.uuid4()), nullable=False
    )
    clerk_user_id = Column(String(64), unique=True, nullable=True)
    slack_user_id = Column(String(64), unique=True, nullable=True)
    email = Column(String(255), unique=True, nullable=False)
    first_name = Column(String(100))
    last_name = Column(String(100))
    role = Column(
        Enum(UserRole, name="user_role"), nullable=True, default=UserRole.EXTERNAL
    )
    is_active = Column(Boolean, nullable=True, default=True)
    created_at = Column(DateTime, default=datetime.utcnow())
    updated_at = Column(DateTime, default=datetime.utcnow(), onupdate=datetime.utcnow())
    profile = Column(JSONB, default=dict)

    conversations = relationship("Conversation", back_populates="user")
    cv_list = relationship("CVData", back_populates="user")
    workflows = relationship("WorkflowRecord", back_populates="user")

    # Applications where this user is the candidate
    applications = relationship(
        "Application",
        back_populates="candidate",
        foreign_keys="Application.candidate_id",
    )
