"""Health check endpoints for monitoring and deployment verification."""

import asyncio
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

from gentekai.db.session import get_db_session
from gentekai.config import settings

router = APIRouter()


@router.get("/health")
async def health_check() -> Dict[str, Any]:
    """
    Basic health check endpoint.
    Returns 200 if the service is running.
    """
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "gentekai-backend",
        "version": "1.0.0"
    }


@router.get("/health/detailed")
async def detailed_health_check(
    db: AsyncSession = Depends(get_db_session)
) -> Dict[str, Any]:
    """
    Detailed health check including database connectivity.
    """
    health_status = {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "gentekai-backend",
        "version": "1.0.0",
        "checks": {}
    }
    
    # Database connectivity check
    try:
        start_time = datetime.utcnow()
        result = await db.execute(text("SELECT 1"))
        end_time = datetime.utcnow()
        response_time = (end_time - start_time).total_seconds() * 1000
        
        health_status["checks"]["database"] = {
            "status": "healthy",
            "response_time_ms": round(response_time, 2),
            "message": "Database connection successful"
        }
    except Exception as e:
        health_status["status"] = "unhealthy"
        health_status["checks"]["database"] = {
            "status": "unhealthy",
            "error": str(e),
            "message": "Database connection failed"
        }
    
    # Environment check
    health_status["checks"]["environment"] = {
        "status": "healthy",
        "database_url_configured": bool(settings.DATABASE_URL),
        "sql_tool_url_configured": bool(settings.SQL_TOOL_URL),
        "message": "Environment variables loaded"
    }
    
    # If any check failed, return 503
    if health_status["status"] == "unhealthy":
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=health_status
        )
    
    return health_status


@router.get("/health/ready")
async def readiness_check(
    db: AsyncSession = Depends(get_db_session)
) -> Dict[str, Any]:
    """
    Readiness check for Kubernetes/container orchestration.
    Checks if the service is ready to accept traffic.
    """
    checks = []
    all_ready = True
    
    # Database readiness
    try:
        await db.execute(text("SELECT 1"))
        checks.append({
            "name": "database",
            "status": "ready",
            "message": "Database is accessible"
        })
    except Exception as e:
        all_ready = False
        checks.append({
            "name": "database",
            "status": "not_ready",
            "error": str(e)
        })
    
    # Configuration readiness
    if not settings.DATABASE_URL:
        all_ready = False
        checks.append({
            "name": "configuration",
            "status": "not_ready",
            "error": "DATABASE_URL not configured"
        })
    else:
        checks.append({
            "name": "configuration",
            "status": "ready",
            "message": "Required configuration present"
        })
    
    result = {
        "ready": all_ready,
        "timestamp": datetime.utcnow().isoformat(),
        "checks": checks
    }
    
    if not all_ready:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=result
        )
    
    return result


@router.get("/health/live")
async def liveness_check() -> Dict[str, Any]:
    """
    Liveness check for Kubernetes/container orchestration.
    Checks if the service is alive and should not be restarted.
    """
    return {
        "alive": True,
        "timestamp": datetime.utcnow().isoformat(),
        "uptime_seconds": 0,  # Could be implemented with startup time tracking
        "message": "Service is alive"
    }


@router.get("/metrics")
async def metrics_endpoint() -> Dict[str, Any]:
    """
    Basic metrics endpoint for monitoring.
    """
    # This could be expanded with actual metrics collection
    return {
        "timestamp": datetime.utcnow().isoformat(),
        "metrics": {
            "requests_total": 0,  # Could be tracked with middleware
            "requests_per_second": 0,
            "response_time_avg": 0,
            "active_connections": 0,
            "memory_usage_mb": 0,
            "cpu_usage_percent": 0
        },
        "status": "metrics_available"
    }
