#!/bin/bash

# Simple deployment script for CI/CD
# Used by GitHub Actions workflow

set -e

# Configuration
PROJECT_NAME="gentekai-backend"
DOCKER_COMPOSE_FILE="docker-compose.prod.yml"
HEALTH_CHECK_URL="http://localhost:9876/api/v1/health"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# Health check
health_check() {
    log "Performing health check..."
    
    for i in {1..10}; do
        if curl -f "$HEALTH_CHECK_URL" >/dev/null 2>&1; then
            success "Health check passed!"
            return 0
        fi
        log "Waiting for application... ($i/10)"
        sleep 10
    done
    
    error "Health check failed!"
    return 1
}

# Simple deploy for CI/CD
deploy() {
    log "Starting deployment..."
    
    # Stop current containers
    log "Stopping current containers..."
    docker compose -f "$DOCKER_COMPOSE_FILE" down --timeout 30
    
    # Start new deployment
    log "Starting new deployment..."
    docker compose -f "$DOCKER_COMPOSE_FILE" up -d
    
    # Wait and health check
    log "Waiting for services to start..."
    sleep 30
    
    if health_check; then
        success "Deployment completed successfully!"
        docker compose -f "$DOCKER_COMPOSE_FILE" ps
    else
        error "Deployment failed!"
        docker compose -f "$DOCKER_COMPOSE_FILE" logs --tail=50
        return 1
    fi
}

# Execute
case "${1:-deploy}" in
    "deploy")
        deploy
        ;;
    "health")
        health_check
        ;;
    *)
        echo "Usage: $0 {deploy|health}"
        exit 1
        ;;
esac
