#!/bin/bash

# GenTekai Backend Deployment Script
# This script handles deployment operations for the GenTekai backend service

set -e  # Exit on any error

# Configuration
PROJECT_NAME="gentekai-backend"
DOCKER_COMPOSE_FILE="docker-compose.prod.yml"
HEALTH_CHECK_URL="http://localhost:9876/api/v1/health"
BACKUP_DIR="backup"
LOG_DIR="logs"
MAX_BACKUPS=5
HEALTH_CHECK_TIMEOUT=300  # 5 minutes
HEALTH_CHECK_INTERVAL=10  # 10 seconds

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if required commands exist
check_dependencies() {
    local deps=("docker" "curl")
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            error "$dep is required but not installed."
            exit 1
        fi
    done

    # Check if docker compose is available
    if ! docker compose version &> /dev/null; then
        error "docker compose is required but not available."
        exit 1
    fi
}

# Create necessary directories
setup_directories() {
    log "Setting up directories..."
    mkdir -p "$BACKUP_DIR" "$LOG_DIR"
}

# Backup current deployment
backup_deployment() {
    log "Creating backup of current deployment..."
    
    local timestamp=$(date +%Y%m%d-%H%M%S)
    local backup_file="$BACKUP_DIR/deployment-$timestamp"
    
    # Backup container information
    if docker compose -f "$DOCKER_COMPOSE_FILE" ps -q | grep -q .; then
        docker compose -f "$DOCKER_COMPOSE_FILE" ps > "$backup_file-containers.txt"
        docker images --format 'table {{.Repository}}\t{{.Tag}}\t{{.ID}}\t{{.CreatedAt}}' > "$backup_file-images.txt"
        success "Backup created: $backup_file"
    else
        warning "No running containers to backup"
    fi
    
    # Clean old backups
    cleanup_old_backups
}

# Clean up old backups
cleanup_old_backups() {
    log "Cleaning up old backups (keeping last $MAX_BACKUPS)..."
    cd "$BACKUP_DIR"
    ls -t deployment-*.txt 2>/dev/null | tail -n +$((MAX_BACKUPS * 2 + 1)) | xargs -r rm
    cd - > /dev/null
}

# Health check function
health_check() {
    log "Performing health check..."
    
    local elapsed=0
    while [ $elapsed -lt $HEALTH_CHECK_TIMEOUT ]; do
        if curl -f "$HEALTH_CHECK_URL" >/dev/null 2>&1; then
            success "Health check passed!"
            return 0
        fi
        
        log "Waiting for application to be ready... ($elapsed/$HEALTH_CHECK_TIMEOUT seconds)"
        sleep $HEALTH_CHECK_INTERVAL
        elapsed=$((elapsed + HEALTH_CHECK_INTERVAL))
    done
    
    error "Health check failed after $HEALTH_CHECK_TIMEOUT seconds"
    return 1
}

# Deploy function
deploy() {
    log "Starting deployment..."
    
    # Create backup
    backup_deployment
    
    # Stop current containers gracefully
    log "Stopping current containers..."
    docker compose -f "$DOCKER_COMPOSE_FILE" down --timeout 30

    # Clean up old images (keep last 3)
    log "Cleaning up old images..."
    docker images "$PROJECT_NAME" --format '{{.ID}} {{.CreatedAt}}' | sort -k2 -r | tail -n +4 | awk '{print $1}' | xargs -r docker rmi || true

    # Start new deployment
    log "Starting new deployment..."
    docker compose -f "$DOCKER_COMPOSE_FILE" up -d

    # Wait for services to be ready
    log "Waiting for services to start..."
    sleep 30

    # Verify deployment
    if docker compose -f "$DOCKER_COMPOSE_FILE" ps | grep -q 'Up'; then
        success "Containers started successfully!"
        docker compose -f "$DOCKER_COMPOSE_FILE" ps
    else
        error "Some containers failed to start!"
        docker compose -f "$DOCKER_COMPOSE_FILE" logs --tail=50
        return 1
    fi
    
    # Health check
    if health_check; then
        success "Deployment completed successfully!"
    else
        error "Deployment failed health check!"
        return 1
    fi
}

# Rollback function
rollback() {
    log "Starting rollback..."
    
    # Get previous image
    local previous_image=$(docker images "$PROJECT_NAME" --format '{{.ID}} {{.CreatedAt}}' | sort -k2 -r | sed -n '2p' | awk '{print $1}')
    
    if [ -z "$previous_image" ]; then
        error "No previous image found for rollback!"
        return 1
    fi
    
    log "Rolling back to image: $previous_image"

    # Stop current containers
    docker compose -f "$DOCKER_COMPOSE_FILE" down --timeout 30

    # Tag previous image as latest
    docker tag "$previous_image" "$PROJECT_NAME:latest"

    # Start with previous image
    docker compose -f "$DOCKER_COMPOSE_FILE" up -d
    
    # Health check
    sleep 30
    if health_check; then
        success "Rollback completed successfully!"
    else
        error "Rollback failed!"
        return 1
    fi
}

# Status function
status() {
    log "Checking application status..."
    
    echo "=== Container Status ==="
    docker compose -f "$DOCKER_COMPOSE_FILE" ps

    echo ""
    echo "=== Resource Usage ==="
    docker stats --no-stream --format 'table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}'
    
    echo ""
    echo "=== Health Check ==="
    if curl -f "$HEALTH_CHECK_URL" 2>/dev/null; then
        success "Application is healthy"
    else
        error "Application health check failed"
    fi
    
    echo ""
    echo "=== Recent Images ==="
    docker images "$PROJECT_NAME"
}

# Logs function
show_logs() {
    local lines=${1:-100}
    log "Showing last $lines lines of logs..."
    docker compose -f "$DOCKER_COMPOSE_FILE" logs --tail="$lines"
}

# Restart function
restart() {
    log "Restarting application..."
    docker compose -f "$DOCKER_COMPOSE_FILE" restart
    
    sleep 30
    if health_check; then
        success "Restart completed successfully!"
    else
        error "Restart failed!"
        return 1
    fi
}

# Main function
main() {
    check_dependencies
    setup_directories
    
    case "${1:-}" in
        "deploy")
            deploy
            ;;
        "rollback")
            rollback
            ;;
        "status")
            status
            ;;
        "logs")
            show_logs "${2:-100}"
            ;;
        "restart")
            restart
            ;;
        "health")
            health_check
            ;;
        *)
            echo "Usage: $0 {deploy|rollback|status|logs [lines]|restart|health}"
            echo ""
            echo "Commands:"
            echo "  deploy   - Deploy the application"
            echo "  rollback - Rollback to previous version"
            echo "  status   - Show application status"
            echo "  logs     - Show application logs (default: 100 lines)"
            echo "  restart  - Restart the application"
            echo "  health   - Perform health check"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
