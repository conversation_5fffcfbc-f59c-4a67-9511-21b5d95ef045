# GenTekai Backend Deployment Guide

This document describes the CI/CD pipeline and deployment process for the GenTekai Backend service.

## Overview

The deployment system uses GitHub Actions for automated CI/CD with the following features:

- **Automated Testing**: Runs tests on every push and pull request
- **Docker Build**: Builds and caches Docker images
- **Automated Deployment**: Deploys to production on main/master branch
- **Manual Operations**: Manual deployment, rollback, and monitoring
- **Health Checks**: Comprehensive health monitoring
- **Rollback Support**: Quick rollback to previous versions

## Prerequisites

### Server Setup

1. **Docker & Docker Compose**: Installed on the target server
2. **SSH Access**: SSH key-based authentication configured
3. **Directory Structure**: 
   ```
   ~/deployments/gentekai-backend/
   ├── backup/          # Deployment backups
   ├── logs/            # Application logs
   ├── docker-compose.yml
   └── scripts/
   ```

### GitHub Secrets

Configure the following secrets in your GitHub repository:

| Secret | Description | Example |
|--------|-------------|---------|
| `SSH_HOST` | Server hostname or IP | `your-server.com` |
| `SSH_USER` | SSH username | `deploy` |
| `SSH_PRIVATE_KEY` | SSH private key | `-----BEGIN OPENSSH PRIVATE KEY-----...` |

## Workflows

### 1. Main Deployment Workflow (`.github/workflows/deploy.yml`)

**Triggers:**
- Push to `main` or `master` branch
- Merged pull requests

**Jobs:**
- **Test**: Runs linting, tests, and coverage
- **Build**: Builds Docker image with caching
- **Deploy**: Deploys to production server

**Features:**
- Automatic backup before deployment
- Health checks after deployment
- Rollback on failure
- Cleanup of old images and backups

### 2. Manual Operations Workflow (`.github/workflows/manual-deploy.yml`)

**Trigger:** Manual dispatch from GitHub Actions UI

**Available Actions:**
- `deploy`: Deploy specific branch
- `rollback`: Rollback to previous version
- `restart`: Restart application
- `status`: Check application status
- `logs`: View application logs

## Deployment Process

### Automatic Deployment

1. **Push to main/master** or **merge PR**
2. **Tests run** automatically
3. **Docker image built** and cached
4. **Deployment to server**:
   - Backup current deployment
   - Stop current containers
   - Load new image
   - Start new containers
   - Health check verification
   - Cleanup old resources

### Manual Deployment

1. Go to **Actions** tab in GitHub
2. Select **Manual Deployment** workflow
3. Click **Run workflow**
4. Choose:
   - Environment (production/staging)
   - Action (deploy/rollback/restart/status/logs)
   - Branch (for deploy action)
   - Log lines (for logs action)

## Health Checks

The application provides a health check endpoint:

### Endpoint

| Endpoint | Purpose | Auth Required |
|----------|---------|---------------|
| `/api/v1/health` | Application health check with service status | No |

### Docker Health Check

The Docker container includes a built-in health check:
- **Interval**: 30 seconds
- **Timeout**: 10 seconds
- **Start Period**: 60 seconds
- **Retries**: 3

## Server Management

### Using the Deployment Script

The `scripts/deploy.sh` script provides local deployment management:

```bash
# Deploy application
./scripts/deploy.sh deploy

# Check status
./scripts/deploy.sh status

# View logs (last 100 lines)
./scripts/deploy.sh logs

# View logs (custom lines)
./scripts/deploy.sh logs 500

# Restart application
./scripts/deploy.sh restart

# Rollback to previous version
./scripts/deploy.sh rollback

# Health check only
./scripts/deploy.sh health
```

### Manual Server Operations

```bash
# SSH to server
ssh <EMAIL>

# Navigate to deployment directory
cd ~/deployments/gentekai-backend

# Check container status
docker-compose ps

# View logs
docker-compose logs -f

# Restart services
docker-compose restart

# Update and restart
docker-compose pull && docker-compose up -d

# View resource usage
docker stats
```

## Monitoring

### Application Logs

```bash
# Real-time logs
docker-compose logs -f

# Last 100 lines
docker-compose logs --tail=100

# Specific service logs
docker-compose logs backend

# Save logs to file
docker-compose logs > logs/app-$(date +%Y%m%d).log
```

### System Monitoring

```bash
# Container resource usage
docker stats

# Disk usage
df -h

# System resources
htop

# Network connections
netstat -tulpn | grep :9876
```

### Health Monitoring

```bash
# Health check
curl http://localhost:9876/api/v1/health
```

## Troubleshooting

### Common Issues

1. **Deployment Fails**
   - Check GitHub Actions logs
   - Verify SSH connectivity
   - Check server disk space
   - Review application logs

2. **Health Check Fails**
   - Check database connectivity
   - Verify environment variables
   - Review container logs
   - Check port availability

3. **Container Won't Start**
   - Check Docker logs: `docker-compose logs`
   - Verify image exists: `docker images`
   - Check resource usage: `docker stats`
   - Review configuration files

### Recovery Procedures

1. **Quick Rollback**
   ```bash
   # Via GitHub Actions
   Run Manual Deployment workflow with "rollback" action
   
   # Via server script
   ./scripts/deploy.sh rollback
   
   # Manual rollback
   docker-compose down
   docker tag gentekai-backend:previous gentekai-backend:latest
   docker-compose up -d
   ```

2. **Emergency Restart**
   ```bash
   # Restart all services
   docker-compose restart
   
   # Restart specific service
   docker-compose restart backend
   
   # Force recreate containers
   docker-compose down && docker-compose up -d
   ```

3. **Database Issues**
   ```bash
   # Check database container
   docker-compose logs db
   
   # Connect to database
   docker exec -it gtk_db psql -U postgres -d dev_db
   
   # Restart database
   docker-compose restart db
   ```

## Security Considerations

1. **SSH Keys**: Use dedicated deployment keys with minimal permissions
2. **Secrets**: Never commit secrets to repository
3. **Network**: Restrict access to deployment ports
4. **Backups**: Regular backup of database and configurations
5. **Updates**: Keep Docker and system packages updated

## Maintenance

### Regular Tasks

1. **Weekly**:
   - Review deployment logs
   - Check disk usage
   - Update system packages

2. **Monthly**:
   - Clean old Docker images
   - Review and rotate logs
   - Update dependencies

3. **Quarterly**:
   - Security audit
   - Performance review
   - Disaster recovery test

### Backup Strategy

- **Automatic**: Deployment backups before each release
- **Database**: Regular database backups (separate process)
- **Configuration**: Version controlled in Git
- **Retention**: Keep last 5 deployment backups
